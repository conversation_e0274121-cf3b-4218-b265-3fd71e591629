import os, sys
from idna import encode
import pandas as pd
current_dir = os.path.dirname(os.path.abspath(__file__))

# 将项目路径添加到模块搜索路径
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from loguru import logger

import datetime
from misc.Readstockfile import read_remote_file, write_file
from misc.ssh_conn import sftp_clent_wintrader
from misc.utils import re_find, check_split_account_data
from misc.standardize_dev.wuxianyi import standardize_wuxianyi_deal

# from misc.standard_table import standardize_margindeal
# from public import MarginDeal, FutureHold, FutureAccount

accounts_dict = {
    # # '********'     : '远航安心中性2号_兴证期货',
    # # '**********'   : '远航安心中性2号_国投安信期货',
    # # '*********'    : '远航安心中性2号_中信期货',
    # # '********'     : '远航安心中性5号',
    # '********'     : '远航安心中性5号_中电投先融期货',
    # '*********'    : '宽辅联丰专享_平安期货',
    # # '091125'       : '宽辅思贤专享中性1号',
    # # '*********'    : '宽辅思贤专享中性1号_中信',
    # '********'     : '宽辅专享2号_国贸期货',
    # '********'     : '宽辅瑞年专享_国贸期货',
    # '********'     : '宽辅瑞年专享_国君期货',
    # '5000519'      : '宽辅瑞年专享_佛山金控期货',
    # '*********'    : '宽辅金品中国专享_中信期货',
    # # '*********'    : '宽辅500指增1号_中信',
    # # '********'     : '宽辅1000指增1号_华泰',
    # '*********'    : '宽辅专享6号_大有期货',
    # '*********'    : '宽辅泓涛专享1号_中信期货',
    # '2120131'      : '宽辅思贤专享中性2号_瑞达期货',
    # '2120132'      : '宽辅专享2号_瑞达期货',
    # '2120133'      : '宽辅专享7号_瑞达期货',
    # '2120166'      : '远航安心中性1号_瑞达期货',
    # '2120167'      : '宽辅联丰专享_瑞达期货',
    '**********'     : '中性对冲_申万期货',
}

# client_type = 'wuxianyi'

# ==========================================================




def download_wyxy_file(date):
    # 拆分trade文件
    wxy_trade_file = 'Trade.csv'

    trade_file = os.path.join(wxy_dir, dir_name, 'dump', wxy_trade_file)

    df = read_remote_file(trade_file, src_type='wintrader', dtype={
        'InvestorID':str,
        'TradeID':str,
        'OrderSysID':str,
        })


    for acount_name in accounts_dict.keys():
        accont_mask = df['InvestorID'] == acount_name
        account_df = df[accont_mask]

        # print(account_df.head(2))

        deal = standardize_wuxianyi_deal(account_df)

        if not deal.empty:
            write_file(deal,
                    file_type='csv',
                    dest_type='zsdav',
                    dest_path=os.path.join('accounts', accounts_dict[acount_name], 'account', 'futuredeal_{}.csv'.format(date)),
                    index=False
            )

    # check split account data
    exclude_list = list(accounts_dict.keys())
    check_split_account_data(df, colname='InvestorID', exclude_list=exclude_list)






    # 拆分hold文件
    wxy_pos_file = 'PositionDetail.csv'
    pos_file = os.path.join(wxy_dir, dir_name, 'dump', wxy_pos_file)
    df = read_remote_file(pos_file, src_type='wintrader', dtype={
        'InvestorID':str,
        'TradeID':str,
        'TradingDay':str,
    })

    df['Direction'] = df['Direction'].astype(str).map({'0':'多头', '1':'空头'})

    df = df[['InvestorID', 'TradingDay', 'InstrumentID', 'Direction', 'Volume']].rename(columns={
        'InstrumentID':'ticker',
        'Direction':'longshort',
        'Volume':'volume',
        'TradingDay':'date',})
    df = df[pd.to_datetime(df['date']) == date]
    df = df.drop('date', axis=1)
    df = df.groupby(['InvestorID', 'ticker', 'longshort']).sum().reset_index()
    # df['account'] = df['InvestorID'].map(accounts_dict)
    df = df[['InvestorID', 'ticker', 'longshort', 'volume']]

    for account_id in accounts_dict.keys():
        account_mask = df['InvestorID'] == account_id
        account_hold = df[account_mask]
        account_name = accounts_dict[account_id]

        # print(account_df.head(2))
        # if not account_df.empty:
        
        write_file(account_hold,
                file_type='csv',
                dest_type='zsdav',
                dest_path=os.path.join('accounts', account_name, 'hold', 'hold_{}.csv'.format(date)),
                index=False
        )
        
        # hold = FutureHold(data=account_df, source_type='df', date=date)
        # hold.update_data()
        # hold.summarize()
        # save_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.xls')
        # print(account_name)
        # print(hold.future)
        # write_file(hold.future, file_type='xls', dest_type='dav', dest_path=save_hold_path.format(date), index=False)




    # 拆分account 信息
    wxy_account_file = 'InvestorAccount.csv'
    account_file = os.path.join(wxy_dir, dir_name, 'dump', wxy_account_file)
    df = read_remote_file(account_file, src_type='wintrader', dtype={
        'InvestorID':str,
        'TradingDay':str,
    })

    df = df[['InvestorID', 'TradingDay', 'AccountID', 'PreDeposit', 'PreBalance', 'PreMargin',
        'Deposit', 'Withdraw', 'Margin', 'Fee', 'CloseProfit', 'PositionProfit',
        'Available', 'WithdrawQuota', 'Balance', 'CurBalance', 'DynamicRights',
        'FuturePositionProfitFloat']]
    for account_id in accounts_dict.keys():
        account_mask = df['InvestorID'] == account_id
        account_df = df[account_mask]
        account_name = accounts_dict[account_id]

        account_df = account_df[['TradingDay', 'PreDeposit', 'PreBalance',
        'Deposit', 'Withdraw', 'Margin', 'Fee', 'CloseProfit', 'PositionProfit',
        'Available', 'WithdrawQuota', 'Balance', 'DynamicRights',
        'FuturePositionProfitFloat']]

        write_file(account_df,
                file_type='csv',
                dest_type='zsdav',
                dest_path=os.path.join('accounts', account_name, 'account', 'origin_account_info_{}.csv'.format(date)),
                index=False
        )

        # account = FutureAccount(data=account_df, source_type='df', date=date)
        # account.update_data()
        # save_account_path = os.path.join('期货账户', account_name, 'origin_account_info_{}.xls'.format(date))

        # # print(account_df.head(2))
        # # if not account_df.empty:
        # write_file(account.data, file_type='xls', dest_type='dav', dest_path=save_account_path, index=False)
     
     
import tempfile   
def get_futures_market_data(date):
    file = 'MarketData.csv'
    remote_file = os.path.join(wxy_dir, dir_name, 'dump', file)
    # with tempfile.NamedTemporaryFile(delete=True, suffix='.csv') as tmp:
    #     sftp_clent_wintrader.get(remote_file, tmp.name)
        
    #     column_names = pd.read_csv(tmp.name, encoding='gbk',
    #                                         on_bad_lines='skip',  # 跳过有问题的行)
    #     )
    #     print(column_names)
        
        # # 读取原始 CSV（不自动解析列名）
        # df = pd.read_csv(tmp.name, header=None)
        
        # # 过滤掉首列为空的行（假设首列是索引或关键列）
        # cleaned_df = df[df[0].notna()]  # 保留首列非空的行
        
        # # 重新保存为干净的 CSV
        # cleaned_df.to_csv(tmp.name, index=False, header=False)
        # print(f"已清理并保存到: {output_path}")
        
    
    
    df = read_remote_file(remote_file, src_type='wintrader', 
        dtype={
            'InstrumentID':str,
            'TradingDay':str,
        }, 
        encoding='gbk', 
        on_bad_lines='skip', 
        low_memory=False# 跳过有问题的行)
    )
    # print(df.columns)
    # # filter instrument: IC, IF, IH, IM,  
    df = df[(df['ExchangeID'] == 'CFFEX') & (df['InstrumentID'].str.startswith('IC') | df['InstrumentID'].str.startswith('IF') | df['InstrumentID'].str.startswith('IH') | df['InstrumentID'].str.startswith('IM') | df['InstrumentID'].str.startswith('IC') | df['InstrumentID'].str.startswith('IF') | df['InstrumentID'].str.startswith('IH') | df['InstrumentID'].str.startswith('T'))]
    df = df[[
        'TradingDay',
        'InstrumentID',
        'PreSettlementPrice',
        'PreClosePrice',
        'SettlementPrice',
        'ClosePrice',
        'PreOpenInterest',
        'OpenInterest',
        'OpenPrice',
        'HighestPrice',
        'LowestPrice',
        'LastPrice',
        'Volume',
        'Turnover',
    ]]
    df = df.sort_values('InstrumentID')
    write_file(df,
            file_type='csv',
            dest_type='zsdav',
            dest_path=os.path.join('trade_data', 'futures_mktdata', 'futures_mktdata_{}.csv'.format(date)),
            index=False
    )
    
    
if __name__ == '__main__':
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:
        date = datetime.datetime.now().strftime('%Y%m%d')

    wxy_dir = '数据导出/无限易申银万国/'
    wxy_dir_list = sftp_clent_wintrader.listdir(wxy_dir)

    date_dir_list = []
    # 组长 账户 中性2号, 中信
    dir_keyword = '**********_{}'.format(date)
    for item in wxy_dir_list:
        if re_find(dir_keyword, item):
            date_dir_list.append(item)
            logger.info('找到日期目录: {}'.format(item))


    if len(date_dir_list) < 1:
        logger.error('未找到无限易日期目录: {}'.format(date))
        sys.exit(0)
    else:
        date_dir_list.sort(reverse=True)
        dir_name = date_dir_list[0]
        logger.info('使用无限易日期目录: {}'.format(dir_name))


    get_futures_market_data(date)
    download_wyxy_file(date)
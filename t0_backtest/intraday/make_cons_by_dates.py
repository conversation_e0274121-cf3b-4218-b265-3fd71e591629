

from ast import arg
from calendar import Calendar
from turtle import st
from duckdb import OperationalError
from joblib import Parallel, delayed
from pqdm.processes import pqdm
import os, sys
import pandas as pd
import numpy as np
import datetime
import pickle
from pathlib import Path
import warnings

# notebook_dir = Path.cwd() 
# print(str(notebook_dir.parent))
# # sys.path.insert(0, str(notebook_dir.parent))

# print(sys.path)
from backtest import backtest as bt
from tca import tca
from tca import stk_tools
import test

cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import update_xlsx_putdf
from misc.tools import get_stock_close
sys.path.remove(p_dir)

warnings.filterwarnings('ignore') 


trading_dates = [d.strftime('%Y%m%d') for d in Calendar.trading_dates('20240101', '20250601')]
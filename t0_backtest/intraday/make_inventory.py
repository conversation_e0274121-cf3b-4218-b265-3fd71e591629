
from ast import arg
from calendar import Calendar
from turtle import st
from duckdb import OperationalError
from joblib import Parallel, delayed
from pqdm.processes import pqdm
import os, sys
import pandas as pd
import numpy as np
import datetime
import pickle
from pathlib import Path
import warnings

# notebook_dir = Path.cwd() 
# print(str(notebook_dir.parent))
# # sys.path.insert(0, str(notebook_dir.parent))

# print(sys.path)
from backtest import backtest as bt
from tca import tca
from tca import stk_tools
import test

cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import update_xlsx_putdf
from misc.tools import get_stock_close
sys.path.remove(p_dir)

warnings.filterwarnings('ignore') 




def make_pords_by_date(date, ticker_value:int, stock_filter=None,
                       account_type='test_account_w3',
                       start_time:datetime.datetime=None):
    close = get_stock_close(date)
    close['ticker'] = close['ticker'].astype(str).str.zfill(6)
    close['volume'] = (np.floor(ticker_value / close['close'] / 100) * 100).astype(int)
    close['date'] = date
    if stock_filter is not None:
        close = close[close['ticker'].isin(stock_filter)]
    # print(close.tail())

    day_volume = close

    l = []
    for idx, row in day_volume.iterrows():
        po=bt.make_parentorder(row['ticker'], int(row['volume']), account_type, date, start_time=start_time) 
        l.append(po)
    return [date, l]

# l = make_inventroy_by_date('********', 100000)
# print(l)


def make_inventroy_by_dates(folder, test_name, 
                            ticker_value:int, stock_filter=None,
                            start_date='********', end_date='********',
                            account_type='test_account_w3',
                            start_time:datetime.datetime=None):
    trading_dates = [d.strftime('%Y%m%d') for d in Calendar.trading_dates(start_date, end_date)]

    # datas = []
    
    args = []
    for date in trading_dates:
        args.append([date, ticker_value, stock_filter, account_type, start_time])
        
    datas = pqdm(args, make_pords_by_date, n_jobs=5, argument_type='args')
    
    pickle_data = {}
    for data in datas:
        try:
        # if data is None:
        #     continue
            pickle_data[data[0]] = data[1]
        except Exception as e:
            print(e)
            print(data)
    
    stk_tools.pickle_dump(pickle_data, os.path.join(cur_dir, './', f"inventory/{folder}/{test_name}.pkl"))
    

    

datas = make_inventroy_by_dates('Ashare', 'Ashare_100000_w3.5', 
                                ticker_value=100000, 
                                stock_filter=None,
                                start_date='********', end_date='********',
                                account_type='test_account_w3.5',
                                )

import pandas as pd
import sqlalchemy

from misc.tools import DATAYESDB_CONNECTION_STRING

# 1. 读取 Excel 文件（替代 idx_opn_wt_mc_hs）
df_a = pd.read_csv("通联微盘股(沪深)开盘权重_20250626.csv")

# 2. 读取 idx 表（替代 b 表）
engine = sqlalchemy.create_engine(DATAYESDB_CONNECTION_STRING)

with engine.connect() as conn:
    df_b = pd.read_sql("SELECT * FROM idx", engine)

    # 3. 读取 md_security 表（替代 c 表）
    df_c = pd.read_sql("SELECT * FROM md_security", engine)

# 4. 第一次 JOIN（a 和 b）
df_ab = pd.merge(
    df_a,
    df_b,
    left_on="INDEX_ID",
    right_on="SECURITY_ID",
    how="inner",
    suffixes=("_a", "_b")
)

# 5. 第二次 JOIN（ab 和 c）
df_abc = pd.merge(
    df_ab,
    df_c,
    left_on="CONS_ID",
    right_on="SECURITY_ID",
    how="inner",
    suffixes=("_ab", "_c")
)

# 6. WHERE 条件
df_filtered = df_abc[df_abc["EFF_DATE"] == "2023-01-05"].copy()

# 7. 选择列并重命名
df_final = df_filtered[[
    "ID",
    "INDEX_ID",
    "TICKER_SYMBOL_b",
    "SEC_SHORT_NAME_b",
    "CONS_ID",
    "TICKER_SYMBOL_c",
    "SEC_SHORT_NAME_c",
    "EFF_DATE",
    "TOTAL_SHARES",
    "FLOAT_SHARES",
    "FREE_SHARES",
    "REF_OPEN_PRICE",
    "WEIGHT",
    "UPDATE_TIME"
]]

df_final = df_final.rename(columns={
    "TICKER_SYMBOL_b": "TICKER_SYMBOL_INDEX",
    "SEC_SHORT_NAME_b": "SEC_SHORT_NAME_INDEX",
    "TICKER_SYMBOL_c": "TICKER_SYMBOL_CONS",
    "SEC_SHORT_NAME_c": "SEC_SHORT_NAME_CONS"
})

# 8. 输出结果
print(df_final.head())

